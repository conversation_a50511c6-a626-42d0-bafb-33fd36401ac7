from fastapi import Depends, HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, JW<PERSON>rror
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from app.database.connection import SessionLocal
from app.config import SECRET_KEY, ALGORITHM
from app.models.user import User

# ✅ Add HTTPBearer security scheme for Swagger UI token authentication
security = HTTPBearer()

# ✅ Dependency to get a SQLAlchemy DB session
def get_db():
    db = SessionLocal()  # Create DB session
    try:
        yield db  # Yield to route/logic
    finally:
        db.close()  # Close session after request

# ✅ Extract JWT token manually from the Authorization header
# This version is NOT Swagger UI-friendly (used with raw requests)
def get_token_from_header(request: Request):
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header")
    # Return token string after 'Bearer '
    return auth_header.split(" ")[1]

# ✅ Extract JWT token using FastAPI’s HTTPBearer dependency
# This version supports Swagger UI's "Authorize" button
def get_token_from_bearer(credentials: HTTPAuthorizationCredentials = Depends(security)):
    return credentials.credentials  # Return the token string

# ✅ Main dependency: Extracts the user from a valid JWT token (used in protected routes)
# Uses HTTPBearer so it works well with Swagger UI
def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),  # Get token from header
    db: Session = Depends(get_db)  # Inject DB session
):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials"
    )
    try:
        token = credentials.credentials  # Extract the raw token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])  # Decode token

        # Extract user's email (used as subject in the token)
        email = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception  # Invalid or expired token

    # Look up user in DB by email
    user = get_user_by_email(db, email)
    if user is None:
        raise credentials_exception

    return user  # Return authenticated user object

# ✅ Optional: Alternate version using manual header parsing (Swagger UI incompatible)
def get_current_user_custom_header(
    token: str = Depends(get_token_from_header),  # Extract token manually
    db: Session = Depends(get_db)
):
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials"
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = get_user_by_email(db, email)
    if user is None:
        raise credentials_exception

    return user

def get_user_by_email(db: Session, email: str):
    try:
        return db.query(User).options(joinedload(User.roles)).filter(User.email == email).first()
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=str(e))