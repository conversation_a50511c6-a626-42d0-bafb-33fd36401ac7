from sqlalchemy.orm import Session
from app.schemas.agents import <PERSON><PERSON><PERSON>, Agent<PERSON><PERSON>, AgentUpdate
from app.controller.agents import get_agent, get_agents, create_agent, delete_agent, update_agent
from app.utils.permissions import user_has_permission
from app.dependencies.auth_dependencies import get_current_user
from app.database.connection import get_db
from fastapi import APIRouter, Depends, HTTPException, Request
from app.services.logging_service import agent_logging_service
from app.schemas.logs import OperationType, UserInfo

# ✅ Create a router with a prefix and tags for Swagger documentation
router = APIRouter(prefix="/agents", tags=["agents"])

# ✅ Route: Create a new agent
@router.post("/agents/", response_model=AgentOut)
def create_agents(
    agent: AgentCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Generate request ID
    request_id = str(__import__('uuid').uuid4())

    try:
        # Permission check for creating an agent
        if not user_has_permission(current_user.id, "create_agent", db):
            # Log permission denied
            agent_logging_service.log_permission_denied(
                operation_type=OperationType.CREATE_AGENT,
                request_id=request_id,
                user_id=current_user.id,
                permission_name="create_agent"
            )

            # Log API call with failure
            agent_logging_service.log_api_call(
                request=request,
                operation_type=OperationType.CREATE_AGENT,
                current_user=current_user,
                request_id=request_id,
                status_code=403,
                error_message="Access denied"
            )
            raise HTTPException(status_code=403, detail="Access denied")

        # Execute operation
        result = create_agent(db, agent, request_id)

        # Log successful API call
        agent_logging_service.log_api_call(
            request=request,
            operation_type=OperationType.CREATE_AGENT,
            current_user=current_user,
            request_id=request_id,
            status_code=200
        )

        return result

    except HTTPException as e:
        # Log API call with error status
        agent_logging_service.log_api_call(
            request=request,
            operation_type=OperationType.CREATE_AGENT,
            current_user=current_user,
            request_id=request_id,
            status_code=e.status_code,
            error_message=e.detail
        )
        raise

# ✅ Route: Get a list of all agents
@router.get("/agents/", response_model=list[AgentOut])
def read_agents(
    request: Request,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    request_id = str(__import__('uuid').uuid4())

    try:
        # Permission check for reading agents
        if not user_has_permission(current_user.id, "read_agent", db):
            agent_logging_service.log_permission_denied(
                operation_type=OperationType.READ_AGENTS,
                request_id=request_id,
                user_id=current_user.id,
                permission_name="read_agent"
            )

            agent_logging_service.log_api_call(
                request=request,
                operation_type=OperationType.READ_AGENTS,
                current_user=current_user,
                request_id=request_id,
                status_code=403,
                error_message="Access denied"
            )
            raise HTTPException(status_code=403, detail="Access denied")

        result = get_agents(db, request_id)

        agent_logging_service.log_api_call(
            request=request,
            operation_type=OperationType.READ_AGENTS,
            current_user=current_user,
            request_id=request_id,
            status_code=200
        )

        return result

    except HTTPException as e:
        agent_logging_service.log_api_call(
            request=request,
            operation_type=OperationType.READ_AGENTS,
            current_user=current_user,
            request_id=request_id,
            status_code=e.status_code,
            error_message=e.detail
        )
        raise

# ✅ Route: Get a single agent by ID
@router.get("/agents/{agent_id}", response_model=AgentOut)
def read_agent_by_id(
    agent_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    request_id = str(__import__('uuid').uuid4())

    try:
        # Permission check for reading agent
        if not user_has_permission(current_user.id, "read_agent", db):
            agent_logging_service.log_permission_denied(
                operation_type=OperationType.READ_AGENT,
                request_id=request_id,
                user_id=current_user.id,
                permission_name="read_agent"
            )

            agent_logging_service.log_api_call(
                request=request,
                operation_type=OperationType.READ_AGENT,
                current_user=current_user,
                request_id=request_id,
                status_code=403,
                error_message="Access denied"
            )
            raise HTTPException(status_code=403, detail="Access denied")

        db_agent = get_agent(db, agent_id, request_id)

        if db_agent is None:
            agent_logging_service.log_api_call(
                request=request,
                operation_type=OperationType.READ_AGENT,
                current_user=current_user,
                request_id=request_id,
                status_code=404,
                error_message="Agent not found"
            )
            raise HTTPException(status_code=404, detail="Agent not found")

        agent_logging_service.log_api_call(
            request=request,
            operation_type=OperationType.READ_AGENT,
            current_user=current_user,
            request_id=request_id,
            status_code=200
        )

        return db_agent

    except HTTPException as e:
        # Log API call with error status
        agent_logging_service.log_api_call(
            request=request,
            operation_type=OperationType.READ_AGENT,
            current_user=current_user,
            request_id=request_id,
            status_code=e.status_code,
            error_message=e.detail
        )
        raise

# ✅ Route: Update an existing agent
@router.put("/agents/{agent_id}", response_model=AgentOut)
def update_agent_route(
    agent_id: int,
    updated_data: AgentUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Generate request ID and log API call
    request_id = str(__import__('uuid').uuid4())
    agent_logging_service.log_api_call(
        request=request,
        operation_type=OperationType.UPDATE_AGENT,
        current_user=current_user,
        request_id=request_id
    )

    # Extract user info for permission logging
    user_info = UserInfo(
        user_id=current_user.id,
        username=getattr(current_user, 'username', None),
        email=getattr(current_user, 'email', None)
    )

    # Permission check for updating agent
    permission_granted = user_has_permission(current_user.id, "update_agent", db)

    # Log permission check
    agent_logging_service.log_permission_check(
        operation_type=OperationType.UPDATE_AGENT,
        request_id=request_id,
        user_info=user_info,
        permission_granted=permission_granted,
        permission_name="update_agent"
    )

    if not permission_granted:
        raise HTTPException(status_code=403, detail="Access denied")

    updated = update_agent(db, agent_id, updated_data, request_id)
    if not updated:
        raise HTTPException(status_code=404, detail="Agent not found")

    return updated

# ✅ Route: Delete an agent
@router.delete("/agents/{agent_id}")
def delete_agent_route(
    agent_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    # Generate request ID and log API call
    request_id = str(__import__('uuid').uuid4())
    agent_logging_service.log_api_call(
        request=request,
        operation_type=OperationType.DELETE_AGENT,
        current_user=current_user,
        request_id=request_id
    )

    # Extract user info for permission logging
    user_info = UserInfo(
        user_id=current_user.id,
        username=getattr(current_user, 'username', None),
        email=getattr(current_user, 'email', None)
    )

    # Permission check for deleting agent
    permission_granted = user_has_permission(current_user.id, "delete_agent", db)

    # Log permission check
    agent_logging_service.log_permission_check(
        operation_type=OperationType.DELETE_AGENT,
        request_id=request_id,
        user_info=user_info,
        permission_granted=permission_granted,
        permission_name="delete_agent"
    )

    if not permission_granted:
        raise HTTPException(status_code=403, detail="Access denied")

    deleted = delete_agent(db, agent_id, request_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Agent not found")

    return {"detail": "Agent deleted successfully"}
